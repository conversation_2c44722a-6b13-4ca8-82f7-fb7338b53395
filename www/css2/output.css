*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 20px;
  padding-left: 20px;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 1240px) {
  .container {
    max-width: 1240px;
  }
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-1 {
  bottom: 0.25rem;
}

.end-0 {
  inset-inline-end: 0px;
}

.right-0 {
  right: 0px;
}

.right-1 {
  right: 0.25rem;
}

.top-0 {
  top: 0px;
}

.top-\[44px\] {
  top: 44px;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[60\] {
  z-index: 60;
}

.order-first {
  order: -9999;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.m-auto {
  margin: auto;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-\[26px\] {
  margin-top: 26px;
  margin-bottom: 26px;
}

.my-\[50px\] {
  margin-top: 50px;
  margin-bottom: 50px;
}

.-mr-5 {
  margin-right: -1.25rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-1\.5 {
  margin-bottom: 0.375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-11 {
  margin-bottom: 2.75rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-20 {
  margin-bottom: 5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-7 {
  margin-bottom: 1.75rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-\[108px\] {
  margin-bottom: 108px;
}

.mb-\[11px\] {
  margin-bottom: 11px;
}

.mb-\[13px\] {
  margin-bottom: 13px;
}

.mb-\[14px\] {
  margin-bottom: 14px;
}

.mb-\[15px\] {
  margin-bottom: 15px;
}

.mb-\[17px\] {
  margin-bottom: 17px;
}

.mb-\[18px\] {
  margin-bottom: 18px;
}

.mb-\[19px\] {
  margin-bottom: 19px;
}

.mb-\[22px\] {
  margin-bottom: 22px;
}

.mb-\[23px\] {
  margin-bottom: 23px;
}

.mb-\[25px\] {
  margin-bottom: 25px;
}

.mb-\[26px\] {
  margin-bottom: 26px;
}

.mb-\[28px\] {
  margin-bottom: 28px;
}

.mb-\[29px\] {
  margin-bottom: 29px;
}

.mb-\[30px\] {
  margin-bottom: 30px;
}

.mb-\[32px\] {
  margin-bottom: 32px;
}

.mb-\[33px\] {
  margin-bottom: 33px;
}

.mb-\[34px\] {
  margin-bottom: 34px;
}

.mb-\[38px\] {
  margin-bottom: 38px;
}

.mb-\[41px\] {
  margin-bottom: 41px;
}

.mb-\[45px\] {
  margin-bottom: 45px;
}

.mb-\[46px\] {
  margin-bottom: 46px;
}

.mb-\[47px\] {
  margin-bottom: 47px;
}

.mb-\[66px\] {
  margin-bottom: 66px;
}

.mb-\[69px\] {
  margin-bottom: 69px;
}

.mb-\[77px\] {
  margin-bottom: 77px;
}

.mb-\[7px\] {
  margin-bottom: 7px;
}

.me-2 {
  margin-inline-end: 0.5rem;
}

.me-auto {
  margin-inline-end: auto;
}

.ml-0 {
  margin-left: 0px;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-2\.5 {
  margin-left: 0.625rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-\[38px\] {
  margin-left: 38px;
}

.ml-auto {
  margin-left: auto;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-\[-20px\] {
  margin-right: -20px;
}

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[27px\] {
  margin-top: 27px;
}

.mt-\[28px\] {
  margin-top: 28px;
}

.mt-\[2px\] {
  margin-top: 2px;
}

.mt-\[30px\] {
  margin-top: 30px;
}

.mt-\[32px\] {
  margin-top: 32px;
}

.mt-\[35px\] {
  margin-top: 35px;
}

.mt-\[42px\] {
  margin-top: 42px;
}

.mt-\[55px\] {
  margin-top: 55px;
}

.mt-auto {
  margin-top: auto;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-10 {
  height: 2.5rem;
}

.h-36 {
  height: 9rem;
}

.h-8 {
  height: 2rem;
}

.h-\[14px\] {
  height: 14px;
}

.h-\[15px\] {
  height: 15px;
}

.h-\[182px\] {
  height: 182px;
}

.h-\[20px\] {
  height: 20px;
}

.h-\[22px\] {
  height: 22px;
}

.h-\[27px\] {
  height: 27px;
}

.h-\[29px\] {
  height: 29px;
}

.h-\[34px\] {
  height: 34px;
}

.h-\[36px\] {
  height: 36px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[422\.66px\] {
  height: 422.66px;
}

.h-\[44px\] {
  height: 44px;
}

.h-\[47px\] {
  height: 47px;
}

.h-\[60px\] {
  height: 60px;
}

.h-\[64px\] {
  height: 64px;
}

.h-\[711px\] {
  height: 711px;
}

.h-\[80px\] {
  height: 80px;
}

.h-\[87px\] {
  height: 87px;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.max-h-\[231px\] {
  max-height: 231px;
}

.max-h-\[270px\] {
  max-height: 270px;
}

.max-h-\[279px\] {
  max-height: 279px;
}

.max-h-\[297\.66px\] {
  max-height: 297.66px;
}

.max-h-\[40px\] {
  max-height: 40px;
}

.w-10 {
  width: 2.5rem;
}

.w-20 {
  width: 5rem;
}

.w-36 {
  width: 9rem;
}

.w-8 {
  width: 2rem;
}

.w-\[156px\] {
  width: 156px;
}

.w-\[15px\] {
  width: 15px;
}

.w-\[183px\] {
  width: 183px;
}

.w-\[20px\] {
  width: 20px;
}

.w-\[249px\] {
  width: 249px;
}

.w-\[280px\] {
  width: 280px;
}

.w-\[29px\] {
  width: 29px;
}

.w-\[309px\] {
  width: 309px;
}

.w-\[34px\] {
  width: 34px;
}

.w-\[36px\] {
  width: 36px;
}

.w-\[380px\] {
  width: 380px;
}

.w-\[47px\] {
  width: 47px;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[64px\] {
  width: 64px;
}

.w-\[87px\] {
  width: 87px;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.min-w-96 {
  min-width: 24rem;
}

.min-w-\[165px\] {
  min-width: 165px;
}

.min-w-\[300px\] {
  min-width: 300px;
}

.max-w-\[160px\] {
  max-width: 160px;
}

.max-w-\[205px\] {
  max-width: 205px;
}

.max-w-\[325px\] {
  max-width: 325px;
}

.max-w-\[40px\] {
  max-width: 40px;
}

.max-w-\[549px\] {
  max-width: 549px;
}

.max-w-\[584px\] {
  max-width: 584px;
}

.max-w-\[714px\] {
  max-width: 714px;
}

.max-w-\[816px\] {
  max-width: 816px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}

.max-w-96 {
  max-width: 24rem;
}

.max-w-\[900px\] {
  max-width: 900px;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-grab {
  cursor: grab;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[13px\] {
  gap: 13px;
}

.gap-\[14px\] {
  gap: 14px;
}

.gap-\[15px\] {
  gap: 15px;
}

.gap-\[18px\] {
  gap: 18px;
}

.gap-\[25px\] {
  gap: 25px;
}

.gap-\[28px\] {
  gap: 28px;
}

.gap-\[34px\] {
  gap: 34px;
}

.gap-\[35px\] {
  gap: 35px;
}

.gap-\[42px\] {
  gap: 42px;
}

.gap-\[51px\] {
  gap: 51px;
}

.gap-\[56px\] {
  gap: 56px;
}

.gap-\[5px\] {
  gap: 5px;
}

.gap-\[60px\] {
  gap: 60px;
}

.gap-\[7px\] {
  gap: 7px;
}

.gap-\[9px\] {
  gap: 9px;
}

.gap-x-\[7px\] {
  -moz-column-gap: 7px;
       column-gap: 7px;
}

.gap-y-\[13px\] {
  row-gap: 13px;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.self-end {
  align-self: flex-end;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[20px\] {
  border-radius: 20px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.rounded-tr-\[3px\] {
  border-top-right-radius: 3px;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-\[\#\#DFE3E8\] {
  border-color: ##DFE3E8;
}

.border-\[\#DEDEDE\] {
  --tw-border-opacity: 1;
  border-color: rgb(222 222 222 / var(--tw-border-opacity, 1));
}

.border-\[\#DFE3E8\] {
  --tw-border-opacity: 1;
  border-color: rgb(223 227 232 / var(--tw-border-opacity, 1));
}

.border-\[\#E5E5E5\] {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}

.border-\[\#EEE\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity, 1));
}

.border-\[\#F0F0F0\] {
  --tw-border-opacity: 1;
  border-color: rgb(240 240 240 / var(--tw-border-opacity, 1));
}

.border-\[\#F4F4F6\] {
  --tw-border-opacity: 1;
  border-color: rgb(244 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.border-light-2 {
  --tw-border-opacity: 1;
  border-color: rgb(204 208 215 / var(--tw-border-opacity, 1));
}

.border-light-3 {
  --tw-border-opacity: 1;
  border-color: rgb(215 219 224 / var(--tw-border-opacity, 1));
}

.border-light-4 {
  --tw-border-opacity: 1;
  border-color: rgb(225 228 232 / var(--tw-border-opacity, 1));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(188 32 38 / var(--tw-border-opacity, 1));
}

.border-white\/25 {
  border-color: rgb(255 255 255 / 0.25);
}

.border-b-light-4 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(225 228 232 / var(--tw-border-opacity, 1));
}

.border-b-primary {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(188 32 38 / var(--tw-border-opacity, 1));
}

.bg-\[\#210B0B\] {
  --tw-bg-opacity: 1;
  background-color: rgb(33 11 11 / var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-green {
  --tw-bg-opacity: 1;
  background-color: rgb(93 189 32 / var(--tw-bg-opacity, 1));
}

.bg-green-light {
  background-color: rgba(93, 189, 32, 0.20);;
}

.bg-light-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 208 215 / var(--tw-bg-opacity, 1));
}

.bg-light-4 {
  --tw-bg-opacity: 1;
  background-color: rgb(225 228 232 / var(--tw-bg-opacity, 1));
}

.bg-light-6 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 246 / var(--tw-bg-opacity, 1));
}

.bg-orange {
  --tw-bg-opacity: 1;
  background-color: rgb(248 153 42 / var(--tw-bg-opacity, 1));
}

.bg-orange-light {
  background-color: rgba(248, 153, 42, 0.20);;
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(188 32 38 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-\[15px\] {
  padding-left: 15px;
  padding-right: 15px;
}

.px-\[18px\] {
  padding-left: 18px;
  padding-right: 18px;
}

.px-\[30px\] {
  padding-left: 30px;
  padding-right: 30px;
}

.px-\[35px\] {
  padding-left: 35px;
  padding-right: 35px;
}

.px-\[7px\] {
  padding-left: 7px;
  padding-right: 7px;
}

.px-\[9px\] {
  padding-left: 9px;
  padding-right: 9px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-11 {
  padding-top: 2.75rem;
  padding-bottom: 2.75rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-\[13px\] {
  padding-top: 13px;
  padding-bottom: 13px;
}

.py-\[17px\] {
  padding-top: 17px;
  padding-bottom: 17px;
}

.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}

.py-\[60px\] {
  padding-top: 60px;
  padding-bottom: 60px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-\[13px\] {
  padding-bottom: 13px;
}

.pb-\[15px\] {
  padding-bottom: 15px;
}

.pb-\[21px\] {
  padding-bottom: 21px;
}

.pb-\[25px\] {
  padding-bottom: 25px;
}

.pb-\[2px\] {
  padding-bottom: 2px;
}

.pb-\[3px\] {
  padding-bottom: 3px;
}

.pb-\[51px\] {
  padding-bottom: 51px;
}

.pb-\[52px\] {
  padding-bottom: 52px;
}

.pb-\[55px\] {
  padding-bottom: 55px;
}

.pb-\[5px\] {
  padding-bottom: 5px;
}

.pb-\[68px\] {
  padding-bottom: 68px;
}

.pb-\[71px\] {
  padding-bottom: 71px;
}

.pb-\[7px\] {
  padding-bottom: 7px;
}

.pb-\[9px\] {
  padding-bottom: 9px;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-\[18px\] {
  padding-left: 18px;
}

.pl-\[19px\] {
  padding-left: 19px;
}

.pl-\[21px\] {
  padding-left: 21px;
}

.pl-\[9px\] {
  padding-left: 9px;
}

.pr-\[15px\] {
  padding-right: 15px;
}

.pr-\[23px\] {
  padding-right: 23px;
}

.pr-\[32px\] {
  padding-right: 32px;
}

.pr-\[33px\] {
  padding-right: 33px;
}

.pr-\[50px\] {
  padding-right: 50px;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pt-\[14px\] {
  padding-top: 14px;
}

.pt-\[34px\] {
  padding-top: 34px;
}

.pt-\[3px\] {
  padding-top: 3px;
}

.pt-\[42px\] {
  padding-top: 42px;
}

.pt-\[43px\] {
  padding-top: 43px;
}

.pt-\[6px\] {
  padding-top: 6px;
}

.pt-\[77px\] {
  padding-top: 77px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[26px\] {
  font-size: 26px;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.italic {
  font-style: italic;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-\[21px\] {
  line-height: 21px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[24\.5px\] {
  line-height: 24.5px;
}

.leading-\[24\.6px\] {
  line-height: 24.6px;
}

.leading-\[31\.5px\] {
  line-height: 31.5px;
}

.leading-\[34px\] {
  line-height: 34px;
}

.leading-\[35px\] {
  line-height: 35px;
}

.leading-\[39px\] {
  line-height: 39px;
}

.text-\[\#080B10\] {
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity, 1));
}

.text-\[\#646C7C\] {
  --tw-text-opacity: 1;
  color: rgb(100 108 124 / var(--tw-text-opacity, 1));
}

.text-current {
  color: currentColor;
}

.text-grey-description {
  --tw-text-opacity: 1;
  color: rgb(100 108 124 / var(--tw-text-opacity, 1));
}

.text-light-1 {
  --tw-text-opacity: 1;
  color: rgb(173 179 191 / var(--tw-text-opacity, 1));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(188 32 38 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/50 {
  color: rgb(255 255 255 / 0.5);
}

.underline {
  text-decoration-line: underline;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 3px 10px rgba(0, 0, 0, 0.102);
  --tw-shadow-colored: 0 3px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

html {
  color: #080B10;
}

body {
  font-family: "Readex Pro", sans-serif;
}

.hover\:mx-1:hover {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.hover\:scale-\[102\%\]:hover {
  --tw-scale-x: 102%;
  --tw-scale-y: 102%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[103\%\]:hover {
  --tw-scale-x: 103%;
  --tw-scale-y: 103%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[105\%\]:hover {
  --tw-scale-x: 105%;
  --tw-scale-y: 105%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:rounded-lg:hover {
  border-radius: 0.5rem;
}

.hover\:rounded-md:hover {
  border-radius: 0.375rem;
}

.hover\:border-b-2:hover {
  border-bottom-width: 2px;
}

.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(188 32 38 / var(--tw-border-opacity, 1));
}

.hover\:border-b-primary:hover {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(188 32 38 / var(--tw-border-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-light-6:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-hover:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(163 28 34 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-light-hover:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 236 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/10:hover {
  background-color: rgb(188 32 38 / 0.1);
}

.hover\:pb-\[3px\]:hover {
  padding-bottom: 3px;
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(188 32 38 / var(--tw-text-opacity, 1));
}

.focus\:border-\[\#BC2026\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(188 32 38 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(188 32 38 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .md\:top-\[80px\] {
    top: 80px;
  }

  .md\:my-\[32px\] {
    margin-top: 32px;
    margin-bottom: 32px;
  }

  .md\:my-\[42px\] {
    margin-top: 42px;
    margin-bottom: 42px;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:mb-\[106px\] {
    margin-bottom: 106px;
  }

  .md\:mb-\[11px\] {
    margin-bottom: 11px;
  }

  .md\:mb-\[17px\] {
    margin-bottom: 17px;
  }

  .md\:mb-\[32px\] {
    margin-bottom: 32px;
  }

  .md\:mb-\[35px\] {
    margin-bottom: 35px;
  }

  .md\:mb-\[37px\] {
    margin-bottom: 37px;
  }

  .md\:mb-\[39px\] {
    margin-bottom: 39px;
  }

  .md\:mb-\[41px\] {
    margin-bottom: 41px;
  }

  .md\:mb-\[49px\] {
    margin-bottom: 49px;
  }

  .md\:mb-\[50px\] {
    margin-bottom: 50px;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-2\.5 {
    margin-top: 0.625rem;
  }

  .md\:mt-\[32px\] {
    margin-top: 32px;
  }

  .md\:mt-\[42px\] {
    margin-top: 42px;
  }

  .md\:mt-\[50px\] {
    margin-top: 50px;
  }

  .md\:mt-\[72px\] {
    margin-top: 72px;
  }

  .md\:mt-\[73px\] {
    margin-top: 73px;
  }

  .md\:mt-\[74px\] {
    margin-top: 74px;
  }

  .md\:block {
    display: block;
  }

  .md\:inline {
    display: inline;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[140px\] {
    height: 140px;
  }

  .md\:h-\[33px\] {
    height: 33px;
  }

  .md\:h-\[36px\] {
    height: 36px;
  }

  .md\:h-\[40px\] {
    height: 40px;
  }

  .md\:h-\[80px\] {
    height: 80px;
  }

  .md\:h-\[90px\] {
    height: 90px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-\[140px\] {
    width: 140px;
  }

  .md\:w-\[232px\] {
    width: 232px;
  }

  .md\:w-\[33px\] {
    width: 33px;
  }

  .md\:w-\[380px\] {
    width: 380px;
  }

  .md\:w-\[40px\] {
    width: 40px;
  }

  .md\:w-\[90px\] {
    width: 90px;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:min-w-\[272px\] {
    min-width: 272px;
  }

  .md\:max-w-\[50px\] {
    max-width: 50px;
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-\[11px\] {
    gap: 11px;
  }

  .md\:gap-\[15px\] {
    gap: 15px;
  }

  .md\:gap-\[5px\] {
    gap: 5px;
  }

  .md\:gap-\[60px\] {
    gap: 60px;
  }

  .md\:gap-x-3 {
    -moz-column-gap: 0.75rem;
         column-gap: 0.75rem;
  }

  .md\:gap-y-5 {
    row-gap: 1.25rem;
  }

  .md\:border {
    border-width: 1px;
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-2 {
    padding: 0.5rem;
  }

  .md\:p-2\.5 {
    padding: 0.625rem;
  }

  .md\:p-3 {
    padding: 0.75rem;
  }

  .md\:p-\[30px\] {
    padding: 30px;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }

  .md\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-\[44px\] {
    padding-top: 44px;
    padding-bottom: 44px;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-1\.5 {
    padding-bottom: 0.375rem;
  }

  .md\:pb-2 {
    padding-bottom: 0.5rem;
  }

  .md\:pb-\[17px\] {
    padding-bottom: 17px;
  }

  .md\:pb-\[47px\] {
    padding-bottom: 47px;
  }

  .md\:pb-\[51px\] {
    padding-bottom: 51px;
  }

  .md\:pb-\[55px\] {
    padding-bottom: 55px;
  }

  .md\:pb-\[86px\] {
    padding-bottom: 86px;
  }

  .md\:pb-\[9px\] {
    padding-bottom: 9px;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-2 {
    padding-left: 0.5rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-\[38px\] {
    padding-top: 38px;
  }

  .md\:pt-\[43px\] {
    padding-top: 43px;
  }

  .md\:pt-\[70px\] {
    padding-top: 70px;
  }

  .md\:pt-\[7px\] {
    padding-top: 7px;
  }

  .md\:text-\[22px\] {
    font-size: 22px;
  }

  .md\:text-\[26px\] {
    font-size: 26px;
  }

  .md\:text-\[33px\] {
    font-size: 33px;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:leading-7 {
    line-height: 1.75rem;
  }

  .md\:leading-\[22px\] {
    line-height: 22px;
  }

  .md\:leading-\[24\.5px\] {
    line-height: 24.5px;
  }

  .md\:leading-\[31\.5px\] {
    line-height: 31.5px;
  }

  .md\:leading-\[39px\] {
    line-height: 39px;
  }
}

@media (min-width: 1024px) {
  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:w-\[380px\] {
    width: 380px;
  }

  .lg\:w-\[544px\] {
    width: 544px;
  }

  .lg\:max-w-\[325px\] {
    max-width: 325px;
  }

  .lg\:max-w-\[916px\] {
    max-width: 916px;
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-normal {
    justify-content: normal;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-0 {
    gap: 0px;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-\[28px\] {
    gap: 28px;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }
}
