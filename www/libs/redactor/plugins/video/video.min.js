Redactor.add("plugin","video",{translations:{en:{video:"Video","video-html-code":"Video Embed Code or Youtube/Vimeo Link"}},modals:{video:'<form action="">                     <div class="form-item">                         <label for="modal-video-input">## video-html-code ##</label>                         <textarea id="modal-video-input" name="video" style="height: 160px;"></textarea>                     </div>                 </form>'},init:function(e){this.app=e,this.lang=e.lang,this.opts=e.opts,this.toolbar=e.toolbar,this.component=e.component,this.insertion=e.insertion,this.inspector=e.inspector,this.selection=e.selection},onmodal:{video:{opened:function(e,i){$video=i.getField("video"),$video.focus()},insert:function(e,i){var t=i.getData();this._insert(t)}}},oncontextbar:function(e,i){var t=this.inspector.parse(e.target);if(t.isComponentType("video")){var o=t.getComponent(),n={remove:{title:this.lang.get("delete"),api:"plugin.video.remove",args:o}};i.set(e,o,n,"bottom")}},start:function(){var e={title:this.lang.get("video"),api:"plugin.video.open"};this.toolbar.addButtonAfter("image","video",e).setIcon('<i class="re-icon-video"></i>')},open:function(){var e={title:this.lang.get("video"),width:"600px",name:"video",handle:"insert",commands:{insert:{title:this.lang.get("insert")},cancel:{title:this.lang.get("cancel")}}};this.app.api("module.modal.build",e)},remove:function(e){this.component.remove(e)},_insert:function(e){if(this.app.api("module.modal.close"),""!==e.video.trim()&&(e.video=this._matchData(e.video),this._isVideoIframe(e.video))){var i=this.component.create("video",e.video);this.insertion.insertHtml(i)}},_isVideoIframe:function(e){return null!==e.match(/<iframe|<video/gi)},_matchData:function(e){var i='<iframe style="width: 500px; height: 281px;" src="',t='" frameborder="0" allowfullscreen></iframe>';if(this._isVideoIframe(e)){var o=["iframe","video","source"];e=(e=e.replace(/<p(.*?[^>]?)>([\w\W]*?)<\/p>/gi,"")).replace(/<\/?([a-z][a-z0-9]*)\b[^>]*>/gi,function(e,i){return-1===o.indexOf(i.toLowerCase())?"":e})}else e.match(this.opts.regex.youtube)?e=e.replace(this.opts.regex.youtube,i+"//www.youtube.com/embed/$1"+t):e.match(this.opts.regex.vimeo)&&(e=e.replace(this.opts.regex.vimeo,i+"//player.vimeo.com/video/$2"+t));return e}});