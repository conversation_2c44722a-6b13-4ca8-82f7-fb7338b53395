// Load plugins
import autoprefixer from "autoprefixer";
import gulp from "gulp";
import plumber from "gulp-plumber";
import postcss from "gulp-postcss";
import * as sass from "sass";
import gulpSass from "gulp-sass";

// Initialize sass compiler with specific options
const sassCompiler = gulpSass(sass);

// Base variables
const paths = {
  localhost: "http://localhost/kaufino/www/",
  styles: {
    src: "www/scss/**/*.scss",
    dest: "www/css/",
  },
};

// Clean assets
async function clean() {
  const del = (await import("del")).deleteAsync;
  return del(["./www/webtemp/*/"]);
}

// CSS task
export function css() {
  return gulp
    .src(paths.styles.src)
    .pipe(
      plumber({
        errorHandler: function (err) {
          console.log(err.toString());
          this.emit("end");
        },
      })
    )
    .pipe(
      sassCompiler
        .sync({
          outputStyle: "expanded",
          quietDeps: true,
          logger: {
            warn: function (message) {
              if (!message.includes("legacy")) {
                console.warn(message);
              }
            },
          },
        })
        .on("error", sassCompiler.logError)
    )
    .pipe(postcss([autoprefixer()]))
    .pipe(gulp.dest(paths.styles.dest));
}

// Watch files
export function watchFiles() {
  gulp.watch(paths.styles.src, css);
}

// Tasks
export const build = gulp.series(clean, css);
export const watch = gulp.parallel(watchFiles, clean);

// Default task
export default gulp.series(build, watch);
