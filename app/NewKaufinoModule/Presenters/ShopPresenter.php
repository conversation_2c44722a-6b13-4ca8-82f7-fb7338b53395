<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON>ino\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Seo\Entities\PageExtension;
use Ka<PERSON>ino\Model\Shops\ContentBlockFacade;
use Ka<PERSON>ino\Model\Shops\Entities\ContentBlock;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use Ka<PERSON><PERSON>\Model\Shops\ReviewFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette\Utils\Strings;

final class ShopPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	/** @var ReviewFacade @inject */
	public $reviewFacade;

    /** @var ArticleFacade @inject */
    public $articleFacade;

	public function actionShop(Shop $shop): void
	{
		$this->responseCacheTags[] = 'shop/' . $shop->getId() . time();

		if ($shop->isActiveKaufino() === false && !$this->getUser()->isLoggedIn()) {
			$this->redirectPermanent(':Kaufino:Homepage:default');
		}

		if ($shop->isEshop()) {
			$this->setView('shopEshop');
		}

		$this->template->shop = $shop;

		$leaflets = $this->leafletFacade->findLeafletsByShop($shop, 60, true, true, null, false);

		$this->template->leafletsInTop = $leaflets;

		$this->template->offers = $this->offerFacade->findOffersByShop($shop, 20, true, Offer::TYPE_LEAFLET);
		$this->template->coupons = $this->offerFacade->findOffersByShop($shop, 20, false);
		$this->template->cities = $this->geoFacade->findCitiesByShop($shop, 48);

        $this->template->articles = $this->articleFacade->findArticlesByWebsiteAndShops($this->website, [$shop], 20);

		if ($shop->getTag()) {
            $similarShops = $this->shopFacade->findLeafletShopsByTag($shop->getTag(), $shop->isStore(), 18, Website::MODULE_KAUFINO);
		} else {
            $similarShops = $this->shopFacade->findTopLeafletShops($shop->getLocalization(), $shop->isStore(), 18, null, !$shop->isStore());
		}

        $this->template->similarShops = $similarShops;

        $topLeafletsBySimilarShops = [];

        if ($similarShops) {
            $topLeafletsBySimilarShops = $this->leafletFacade->findTopLeafletsByShops($similarShops);
        }

        $this->template->topLeafletsBySimilarShops = $topLeafletsBySimilarShops;

		if ($couponId = $this->getRequest()->getParameter('oid')) {
			$offer = $this->offerFacade->findOffer($couponId);

			if ($offer->isCoupon()) {
				$popupCoupon = $offer;
			}
		}

		$this->template->popupCoupon = isset($popupCoupon) ? $popupCoupon : null;

		$this->template->heading1 = $this->seoGenerator->generateShopHeading1($shop, $this->website);
		$this->template->metaTitle = $this->seoGenerator->generateShopMetaTitle($shop, $this->website);
		$this->template->metaDescription = $this->seoGenerator->generateShopMetaDescription($shop, $this->website);
		$this->template->seoGenerator = $this->seoGenerator;
		$this->template->faqContentBlocks = $this->contentBlockFacade->findFaqContentBlocks($shop, Website::MODULE_KAUFINO);

		$this->template->getAverageShopReview = (function () use ($shop) {
			return $this->reviewFacade->findAverageShopReview($shop);
		});

		$this->template->getCountOfTotalReviews = (function () use ($shop) {
			return $this->reviewFacade->findCountOfShopReviews($shop);
		});

		$this->template->getHeadingFromPageExtension = function(PageExtension $pageExtension) use ($shop) {
			return $this->seoGenerator->generateShopHeadingFromPageExtension($pageExtension, $shop, $this->website);
		};

		$websites = $this->websiteFacade->findActiveWebsites($this->website->getModule());
		$localizations = array_map(function ($website) {
			return $website->getLocalization();
		}, $websites);
		$this->template->shopInternationalVariants = $this->shopFacade->findLeafletShopInternationalVariants($shop, $localizations);

		$contentBlocks = $this->contentBlockFacade->findContentBlocksByShop($shop, Website::MODULE_KAUFINO);

		$length = 0;
        $blocksByType = [];
		/** @var ContentBlock $contentBlock */
		foreach ($contentBlocks as $contentBlock) {
			if ($contentBlock->getType() === 'legacy') {
				continue;
			}

            $blocksByType[$contentBlock->getType()] = $contentBlock;

			$length += $contentBlock->getContent() ? strlen($contentBlock->getContent()) : 0;
		}

        $this->template->contentBlocksAllowed = $length > 50;

        $this->template->contentBlocks = [
            'leaflets' => $blocksByType['flyers'] ?? null,
            'shop' => $blocksByType['about'] ?? null,
            'product' => $blocksByType['products'] ?? null,
    //        'contact' => $blocksByType['contact'] ?? null,
        ];

        $this->template->getFaqContentBlocks = function () use ($contentBlocks) {
            return array_filter($contentBlocks, function ($contentBlock) {
                return Strings::contains($contentBlock->getType(), 'faq_') && $contentBlock->getContent() !== null && strlen($contentBlock->getContent()) > 10;
            });
        };
	}
}
