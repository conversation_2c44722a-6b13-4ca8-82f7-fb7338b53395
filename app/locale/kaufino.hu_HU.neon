navbar:
	shops: Üzletek
	leaflets: <PERSON><PERSON><PERSON><PERSON>
	offers: <PERSON><PERSON><PERSON><PERSON>
	search:
		placeholder: Üzletek keresése
		submit: Keresés

	moreShops: További üzletek
	articles: Magazin
	cities: Városok
	home: Főoldal

error404:
    title: 'Oldal nem található'
    text: 'A keresett oldal nem található. Kérjük, pr<PERSON><PERSON><PERSON><PERSON><PERSON>.'
    link: 'Vissza a főoldalra'
    linkToShop: 'Vissza a %brand%'

footer:
	copyright: Kaufino Mind<PERSON> jog fenntartva.
	shops: Üzletek
	category: Kategóriák
	offersCategory: <PERSON><PERSON><PERSON><PERSON>
	aboutKaufino: A Kaufino-ról
	cookies: Sütik
	leaflets: <PERSON><PERSON><PERSON><PERSON>
	aboutUs: Rólunk
	nextCountries: További országok

search:
	title: Keresési eredmények"%query%"
	noResults: 'Bárhogy is kerestük, semmit sem találtunk.'

cityPicker:
	submit: V<PERSON>lasszon
	confirm: <PERSON><PERSON><PERSON> k<PERSON>tva
	error: A helyszínre való bejutást nem engedélyezték
	myLocation: Az én helyem
	placeholder: Select an option

homepage:
	title: Kaufino | akciós újságok
	metaTitle: Online akciós újságok a legismertebb áruházaktól | Kaufino
	metaDescription: 'Fedezze fel a Lidl, Tesco és más áruházak legfrissebb akciós újságait online! Nézze meg a legjobb ajánlatokat gyorsan és egyszerűen a Kaufino-n!'
	text: 'Tekintse meg a hazai hiper- és szupermarketek legújabb akciós újságait online.  Nálunk mindig megtalálja a legjobb ajánlatokat a Lidl, az Aldi és sok más üzlet akciós újságában.'
	and: és
	leaflets: Akciós újságok
	allLeaflets: Minden akciós újság
	shops: Üzletek
	allShops: Minden üzlet
	offersCategory: Akciós áru
	city: Akciós újságok az Ön városában
	articles:
		title: Cikkek a magazinból

	bottomText:
		mainTitle: Hogyan működik a Kaufino
		section1:
			title: Kedvezményes vásárlás
			text: 'Ha szeret kedvezményesen vásárolni és kihasználná az akciós árakat, akkor a Kaufino tökéletes választás az Ön számára! Nálunk egy helyen találja meg kedvenc üzletei <a href="./uzletek">akciós újságainak</a> széles választékát, így könnyedén hozzáférhet a legjobb ajánlatokhoz és kedvezményekhez. Kínálatunkban megtalálja az élelmiszerek, italok, dekorációk, valamint bútorok és szabadidős felszerelések legjobb ajánlatait. Ráadásul minden üzletnél részletes információkat talál a kínált termékek kategóriáiról, az elérhető üzletekről és azok nyitvatartási idejéről is. Vásárlását még sosem volt ilyen egyszerű és kényelmes megtervezni a Kaufino segítségével!'

		section2:
			title: Az összes akciós újság egy helyen
			text: 'Rendszeresen frissítjük és összegyűjtjük Önnek a legfrissebb akciós újságokat és kedvezményeket, hogy minden kedvező ajánlatot egy helyen megtalálhasson. Válogathat a legnagyobb szupermarketek és hipermarketek akciós újságaiból, mint például a <a href="./lidl">Lidl</a>, Penny, <a href="./aldi">Aldi</a> vagy a Tesco, amelyek mindig minőségi élelmiszereket, valamint háztartási cikkeket és szabadidős felszereléseket kínálnak. Ha otthona vagy kertje berendezését és dekorációját szeretné felújítani, tekintse meg a <a href="./jysk">Jysk</a>, Pepco vagy a KiK akciós újságait. Többé nem kell fáradságosan keresgélnie, mert nálunk mindent egy helyen megtalál.'

		section3:
			title: Minden egyszerű és átlátható
			text: 'A Kaufino segítségével a vásárlás most könnyebb, mint valaha. Minden információt átláthatóan előkészítettünk Önnek, így mostantól otthona kényelméből tervezheti meg vásárlásait, anélkül hogy a postaládájában található szórólapokat kéne válogatnia. Egyszerűen válassza ki azt az üzletet, ahol vásárolni szeretne, majd kattintson az aktuális akciós újságra. A szórólapok az elsőtől az utolsó oldalig elérhetőek teljes egészükben, így kényelmesen lapozgathatja és kiválaszthatja kedvenc termékeit <a href="./esemenyek">akciós áron</a>. Emellett az egyes termékeket más hasonló profilú üzletek akciós újságaiban is megkeresheti, és összehasonlíthatja az árakat vagy az egyéb ajánlatokat.'

		section4:
			title: Spóroljon pénzt és időt
			text: 'A heti családi bevásárlás nemcsak fárasztó lehet, hanem a pénztárcáját is megterhelheti. Használja ki az akciókat minden lehetséges termékkategóriában, és kezdje el a tudatos vásárlást a Kaufino segítségével. Keresse meg a szükséges termékeket, és mindig írja össze a bevásárlólistáját. Ez a két alapvető lépés segít abban, hogy jelentős összegeket takarítson meg, és ebben a Kaufino is támogatja Önt. Egyszerűen válassza ki azt az üzletet, ahol a leggyakrabban vásárol, majd tekintse meg az <a href="./akcios-ujsagok">aktuális akciós újságot</a>, és éljen az akciós ajánlatokkal.'
			text2: 'A kiválasztott akciós újságokban kedvezményeket és kedvezményes kuponokat is talál, amelyek segítségével még többet spórolhat. Tekintse meg például az Aldi, a <a href="./lidl">Lidl</a> vagy az <a href="./auchan">Auchan</a> akciós újságokat, amelyekben helyi beszállítóktól származó minőségi élelmiszereket talál.  Így a bio és helyi termékeket kedvezőbb áron szerezheti be, hogy asztalára mindig minőségi és ízletes étel kerüljön anélkül, hogy azt pénztárcája szenvedné meg.'

leaflets:
	title: Aktuális akciós újságok
	metaTitle: Aktuális akciós újságok online
	text: 'Lapozza át Magyarország népszerű áruházainak akciós újságjait online! Fedezze fel a legjobb ajánlatokat, és vásároljon még kedvezőbb áron!'
	metaDescription: 'Lapozza át Magyarország legnépszerűbb hiper- és szupermarketjeinek akciós újságait, és találja meg a legjobb ajánlatokat a kedvező vásárláshoz!'
	city: Akciós újságok az Ön városában
	expiredTitle: Lejárt akciós újságok
	expiredText: Tekintse meg a legnépszerűbb üzlethálózatok lejárt akciós újságainak kínálatát!
	expiredMetaTitle: Lejárt %brand% szórólapok

leaflet:
	ads: HIRDETÉS
	metaTitle: 'Aktuális %brand% akciós újság, érvényes %validSince% dátumtól %validTill% dátumig'
	metaDesc: 'Tekintse meg az aktuális %brand% akciós újságot, amely %validSince% dátumtól érvényes, és vásároljon kedvező áron még ma!'
	title: '%brand% akciós újság, érvényes %validSince% - %validTill%'
	leaflet: %brand%
	desc: 'Éppen %leafletBrandLink% aktuális akciós újságát nézi, %validSince%-%validTill% érvényességgel. %leafletPageCount% oldalon várják a legjobb kedvezmények.'
	smallTitle: '%brand%, érvényes'
	recommendedLeaflets: Népszerű akciós újságok
	similarLeaflets: További %brand% akciós újságok
	backToLeaflets: Vissza az összes akciós újsághoz
	allBrandLeaflets: Minden akciós újság %brand%
	valid: Aktuális akciós újság
	brandLeafletFrom: '%brand% akciós újság, érvényes'
	titleUnChecked: Aktuális %brand% akciós újság
	metaDescUnChecked: Böngéssze át az aktuális %brand% akciós újságokat!
	descUnChecked: 'Éppen %leafletBrandLink% aktuális akciós újságát nézi, érvényes %validSince%-tól %validTill%-ig. %leafletPageCount% oldalon talál kedvezményeket!'
	metaTitleUnChecked: '%brand% akciós újság, érvényes %validSince%-tól'
	futureLeafletTitle: 'Legújabb %brand% akciós újság⭐érvényes %validSince% dátumtól '
	futureLeafletDescription: 'Fedezze fel a közelgő akciókat a legújabb %brand% akciós újságban!⭐Tervezze meg következő vásárlását, és vásároljon a lehető legkedvezőbben! ✅'
	archivedLeafletTitle: Lejárt %brand% akciós újság %validSince% - %validTill%
	archivedLeafletDescription: '%leafletBrandLink% lejárt akciós újságját nézi, amely %validSince% - %validTill% között volt érvényes. %leafletPageCount% oldal mutatja a korábbi kedvezményeket.'
	expiredLeafletTitle: '%brand% akciós újság, érvényes %validSince%-tól %validTill%-ig'
	expiredLeafletDescription: Az adott %brand% akciós újság már nem érvényes. Érvényessége lejárt %validTill%
	actualLeafletValidSince: 'Az aktuális akciós újság %validSinceDay%, %validSince% dátumtól érvényes. ✅'
	expiredLeafletHeading: Ez az akciós újság már nem érvényes
	expiredLeafletLinkToShop: Az aktuális %brand% akciós újságot itt találja
	leaflets: %brand% akciós újságok
	leafletValidTill: 'Érvényes a következő időpontig: %validTill%'
	imageAltTitleWithDate: Akciós újság %brand% - %leafletName% érvényes %validSince%-tól %validTill%-ig - %page% oldal
	imageAltTitle: Akciós újság %brand% - %leafletName% - %page% oldal

newsletter:
	metaTitle: 'Aktuális %brand% hírlevél, érvényes %validSince% dátumtól'
	metaDesc: 'Nézze meg az aktuális %brand% hírlevelet, amely %validSinceDay%, %validSince% kezdettel érvényes.'
	leaflet: %brand%
	desc: 'Aktuális %leafletBrandLink% hírlevél, érvényes %validSince% dátumtól. A hírlevélben aktuális akciókat és érdekes információkat talál.'
	smallTitle: '%brand% hírlevél, érvényes'
	recommendedLeaflets: Népszerű hírlevelek
	similarLeaflets: Egyéb %brand% hírlevelek
	backToLeaflets: Vissza a hírlevelek listájához
	allBrandLeaflets: Minden hírlevél %brand%

shops:
	title: Akciós árak és szórólapok a legjobb áruházaktól online
	metaTitle: Akciós árak és szórólapok a legjobb áruházaktól online
	text: 'Tekintse meg a legnépszerűbb szupermarketek és hipermarketek kínálatát, amelyek akciós újságait mindennap elhozzuk Önnek online, verhetetlen árakkal.'
	metaDescription: 'Böngéssze a Lidl, Tesco és más népszerű áruházak akciós szórólapjait online! Fedezze fel a legjobb ajánlatokat és verhetetlen árakat egy helyen.'
	otherShops:
		title: További üzletek

shop:
	title: Aktuális %brand% akciós újságok
	showLeaflet: Újság megjelenítése
	text: 'Lapozza át az aktuális %brand% akciós újságot, ahol kedvező áron találhatja meg a kiválasztott termékkínálatot. Ne feledkezzen meg a jövő heti %brand% akciós újságról sem, amelyben új kedvezmények várják!'
	textSubdomain: 'Lapozza át az aktuális %brand% akciós újságot, ahol kedvező áron találhatja meg a kiválasztott termékkínálatot. Ne feledkezzen meg a jövő heti %brand% akciós újságról sem, amelyben új kedvezmények várják!'
	button: Átirányítás %brand% áruházba
	type:
		shopTitle: '{$shopName|upper} akciós újság{if $currentLeafletFromDate}, {$currentLeafletFromDate|date:''Y.n.j''}-tól ✅ aktuális szórólap{/if}'
		shopTitleSubdomain: '{$shopName|upper} akciós újság{if $currentLeafletFromDate}, {$currentLeafletFromDate|date:''Y.n.j''}-tól ✅ aktuális szórólap{/if}'
		eshopTitle: %brand% akció
		eshop: 'Tekintse meg a legfrissebb %brand% akciókat inspirációkkal és kedvezményekkel teli katalógusukban. Az aktuális %brand% akciók mindig elérhetőek, így soha nem marad le a kedvezményes ajánlatokról.'

	noLeaflets: 'Keressük Önnek az akciós újságokat... Kérjük, próbálja később.'
	city: %brand% akciós újságok az Ön városában
	otherShops: További üzletek
	storeLeaflet: %brand% akciós újságok
	alternativeName: Gyakran keresett
	internationalVariants: %brand% akciós újságok az alábbi országokban is elérhetőek
	defaultTitleSuffic: '%shopName% - aktuális akciós újságok, akciók'
	offersAll: Akciós termékek
	offers: %brand% akciós újság ajánlatai
	link: '%brand% akciós újság, %page% o. »'
	expired: Az ajánlat lejárt
	offersLeaflets: Akciós újságok %category% termékkategóriából
	offersExpire: Lejárt akciók %category% termékkategóriából
	otherLeaflets: További %brand% akciós újságok
	faq: Gyakran ismételt kérdések
	metaTitles:
		withFutureLeaflet: '{$shopName|upper} akciós újság {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''Y.m.d''} dátumtól ✅ következő szórólap'
		withCurrentLeaflet: '{$shopName|upper} akciós újság {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''Y.m.d''} dátumtól ✅ aktuális szórólap'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} akciós újság: aktuális szórólap online'

	metaDescriptions:
		withFutureLeaflet: '{$shopName|upper} akciós újság ✅ Lapozza át a következő {$shopName|upper} akciós újságot {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''Y.m.d''}-tól. Online elérhető az e heti {$shopName|upper} akciós újság is.'
		withCurrentLeaflet: '{$shopName|upper} akciós újság lapozható formában elérhető {$currentLeafletFromDate|date:''Y.m.d''}-tól.⭐ Fedezze fel az aktuális kedvezményeket {$shopName|upper} üzleteinek kínálatában!'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} akciós újság lapozható ✅ Lapozza át az új {$shopName|upper} akciós újságot. Online elérhető az aktuális {$shopName|upper} akciós újság az e heti ajánlatokkal.'

tag:
	title: %tag% – akciós újságok
	metaTitle: %tag% – akciós újságok
	titleWithTag: Akciós újságok %tag% kategóriában
	citiesWithTag: Városok %tag% kategóriájú akciós újságokkal
	text: 'Lapozza át az aktuális akciós újságokat %tag% kategóriában! Fedezze fel a legjobb ajánlatokat, és vásároljon kedvező áron.'
	noLeaflets: 'Keressük Önnek az aktuális akciós újságot... Kérjük, próbálja később.'
	otherShops: További üzletek
	offerTagTitle: %tag% akcióban
	offerTagPriceFrom: %price%-tól
	offers:
		title: Akciós %tag% az akciós újságokban
		metaDescription: Tekintse meg az akciós %tag% ajánlatokat a legismertebb üzletek akciós újságaiban. Ne hagyja ki a legfrissebb kedvezményeket és akciós árakat!
		text: 'Fedezze fel az új akciókat %tag% kategóriából az <a href="%leafletLink%">akciós újságokban</a>, és ne maradjon le a kiválasztott termékek kedvezményeiről! A legújabb akciós újságokban szereplő %tag% termékkategória áraiat rendszeresen frissítjük, így könnyen megtalálhatja a <strong>legjobb ajánlatokat.</strong>'
		titleWithBestOffer: %tag% akcióban ⏩ %price%-tól az akciós újságokban
		metaDescriptionWithBestOffer: 'Fedezze fel az aktuális akciós újságok legjobb ajánlatait %tag% kategóriában, és ne maradjon le a kedvezményekről! Az árakat folyamatosan frissítjük!'
		currentLeaflet: Aktuális %brand% szórólap

about:
	title: Rólunk
	text: 'Célunk, hogy időt és pénzt takarítsunk meg felhasználóink számára. Minden nap elhozzuk a legnépszerűbb üzlethálózatok aktuális akciós újságait, hogy megkönnyítsük az akciós ajánlatok keresését.'
	address: 'Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699 <br><br> info[at]vimvic.cz'

coupon:
	type:
		sale: Kupon

	showCode: Mutasd a kuponkódot
	valid: Érvényes
	copyText: 'Másold ki a kódot, és illeszd be a <strong>%brand%</strong> áruház kosarába.'
	copy: Másolás
	copied: Másolva
	button: Spórolj %brand% üzletében

offer:
	title: Akciós termékek a legjobb áron
	text: 'Fedezze fel a legújabb akciós termékeket élelmiszer, háztartási cikkek és egyéb kategóriákban! Vásároljon kedvező áron, egyszerűen!'
	metaDescription: 'Fedezze fel a legújabb akciós termékeket élelmiszer, háztartási cikkek és egyéb kategóriákban, a Lidl, Aldi, Tesco és más üzletek kínálatában!'
	whatOnSale: Mi van akcióban?
	offerItem:
		validTillDays: '1 nap maradt|Érvényes %count% napig|Érvényes %count% napig'
		validTillToday: Csak ma
		validTillWeek: Több mint egy hét maradt
		validSinceDays: Érvényes holnaptól|Érvényes %count% nap múlva|Érvényes %count% nap múlva
		validSinceWeek: Érvényes több mint egy hétig

	futureOffers: 'Kedvezmények a jövőbeli szórólapokon a(z) %name% termékre'
	currentOffers: '%name% akcióban - kiválasztott aktuális ajánlatok'
	priceHistory: 'A termék árának alakulása %name% az elmúlt 12 hónapban'
	graphLabel:
		price: 'Ár'
		month: 'Hold'
		currency: 'Ft'
		discount: 'Átlagos kedvezmény'
		offers: 'Ajánlatok száma'

city:
	city:
		metaTitle: %city% üzleteinek és szupermarketjeinek listája
		title: %city% üzletei és szupermarketei
		text: 'Akutális akciós újságok %city% városában. A %stores% üzletek akciós újságaiban, %city% városában számos kedvezményt és akciós terméket talál, amelyek online is elérhetőek.'
		metaDescription: 'Tekintse meg %city% üzleteinek, hipermarketjeinek, szupermarketjeinek és élelmiszerboltjainak listáját, és vásároljon kedvező áron a közelben!'
		text2: 'A legjobb akciók és kedvezmények várják %city% városában! Válogasson a helyi üzletek legfrissebb ajánlatai közül, és találja meg a legjobb árakat minden vásárlásához. %stores% és sok más népszerű üzlethálózat akciós újságai mindössze egy kattintásra vannak Öntől! A Kaufino.com segítségével egyszerűen áttekintheti %city% legkedveltebb üzleteinek aktuális ajánlatait, akár élelmiszert, háztartási cikkeket, divatárut vagy elektronikai eszközöket keres.'
		h2: Akciós újságok és kedvezmények %city% városában
		storesTitle: 'Fióktelepek %city% városában '
		storesMoreButton: További fiókok »
		actualLeaflet: Jelenlegi szórólapok
		leafletStores:
			title: %brand% akciós újság
			store: %brand% %city% akciós újság

		otherShops: Üzletek %city% településen
		nearestCity: További városok a környéken akciós újságokkal
		nearestCityWithShop: További városok a környéken %shopName% akciós újságokkal
		categoriesInText: %category% akciós újságok
		citiesInText: %city% akciós újságai
		generatedText:
			1: '%city% városában, amelynek lakosága %population%, számtalan üzlet széleskörű, akciós választékát fedezheti fel. Emellett nemcsak %city% üzleteinek ajánlatai között találhat kedvezményeket, hanem a környező településeken is további nagyszerű akciók várják, köztük %cities% városaiban. Vásárlás előtt böngésszen a helyi szupermarketek, elektronikai üzletek, barkácsáruházak és divatáruházak ajánlatai között, s találja meg a legjobb akciókat és kedvezményeket!'
			2: 'Fedezze fel %city% város legújabb akcióit és ajánlatait itt:'
			3: 'Ezen kívül további üzletek akciós újságai is elérhetőek a Kaufino.com-on, mint például %stores% és más kedvelt üzlethálózatok szórólapjai. Fedezze fel az %shopsLink% oldalon található %month% havi ajánlatokat, és tervezze meg vásárlásait hatékonyan!'
			leaflet: 'Aktuális %brand% akciós újság, érvényes %validSince% - %validTill%'
			and: és
			or: vagy

	store:
		store: %fullAddress%.
		h1: %brand% %city% %address%
		h2: %brand% üzletek %city% városában
		openHoursAndContact: "Nyitvatartás és kapcsolat"
		title: '%brand% %city% %address% - nyitvatartás 🕔'
		description: 'Hasonlítsa össze az akciós újságok ajánlatait, tájékozódjon meg a pontos címről, nyitvatartásról, és fedezze fel %brand% %address% üzlet kínálatát.'
		open: Nyitva
		closed: Zárva
		text: 'A %brand% üzlet, %address% címen, rendszeresen kínál kedvező akciókat és leárazásokat változatos termékkínálatára, ahol a vásárlás során felhasználhatja a népszerű %brand% akciós újságot.'
		text2: 'Az ügyfelek kényelmesen megtekinthetik online, csakúgy, mint a %stores% üzletekben elérhető akciókat'
		text2WithoutStores: 'Az ügyfelek ezt kényelmesen megtekinthetik online, akárcsak a többi üzletben elérhető akciókat.'
		h2bottom: '%brand% %city%, %street%. akciós újság'
		or: vagy
		others: és mások
		textBottom: '%brand% üzlet, %address% címen, nemcsak széles termékkínálattal, hanem alacsony árakkal is várja Önt, amelyekről rendszeresen tájékoztat az aktuális %brand% akciós újság. A kedvelt %brand% üzlet %city% városában, %street% címen, kedvelt helyszín azok számára, akik kedvező ajánlatokat keresnek. Mivel a %brand% akciós újság online is elérhető, mindig naprakész lehet az aktuális kedvezményekkel kapcsolatban. Amennyiben a %brand% üzlet, %address% címen, nem kínál mindent, amire szüksége van, a környéken további üzletek is rendelkezésre állnak az Ön számára, mint például:'
		textBottom2: 'Tájékozódjon a népszerű üzletek pontos címéről, a vevőszolgálat elérhetőségéről és a nyitvatartási időkről egy helyen, átlátható formában. Emellett információt talál arról is, hogy mely üzletek találhatók az Ön közelében, és hol érheti el a kiválasztott üzletek akciós újságaiban szereplő kedvező ajánlatokat.'
		sections:
			leaflets: További akciós újságok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% fióktelepek a környéken

	shop:
		title: %brand% %city% üzletek
		metaTitle: '%brand% %city% üzletek – címek, nyitvatartás'
		storesTitle: %brand% fiókjai %city% városban
		text: '%brand% akciós újságok és aktuális kedvezmények %city% városában. A %brand% %city% akciós újságában széles termékkínálatot talál a legjobb árakon. %city% városában azonban nemcsak a népszerű %brand% üzlet várja Önt. További kedvelt boltok közé tartozik például a(az) %stores% is, ahol szintén nagyszerű ajánlatokra bukkanhat.'
		metaDescription: 'Keresse meg a legközelebbi %brand% üzleteket %city% városában. Teljes üzletlista, nyitvatartás és aktuális akciós termékek – vásároljon egyszerűen!'
		h2: '%brand% %city% akciós újságok, kedvezmények és akciók'
		leafletStores:
			title: '%brand% %city% akciós újság '
			store: %brand% %city%

		cityLink: %city% akciós újságok
		shopLink: %shop% akciós újságok
		otherShops: További üzletek %city% városban
		shopLeaflet: %brand% akciós újságok
		citiesInText: %brand% %city% akciós újság
		offers: %brand% akciós termékek
		generatedText:
			1: 'A(az) %brand% üzlet %city% városában rendszeresen frissített akciós újságokat kínál, amelyeket minden héten elérhetővé teszünk az Ön számára.'
			2: 'Ha a legújabb %brand%, akciós újságot keresi, erre a linkre kattintva megtekintheti: <a href="%actualLeafletUrl%"> %brand% %city% aktuális szórólap</a>. A szórólap %validSince% és %validTill% között érvényes. Minden akciós újságban érdekes kedvezmények, szezonális akciók, klubárak és széles termékkínálat várja.'
			3: 'A %brand% akciós újságai azonban nemcsak %city% városában érvényesek. Az ajánlatok más közeli fióktelepeken is érvényesek, például %stores% boltokban. Minden akciós újság elérhető az <a href="%leafletsUrl%">Akciós újságok</a> szekcióban.'
			4: 'Ha további üzleteket keres, népszerűek például %stores% üzletei.'

articles:
	title: Cikkek a hipermarketek és szupermarketek akciós újságairól
	h1: Magazin
	description: Cikkek tele tippekkel a könnyebb vásárláshoz a hazai hiper- és szupermarketek új szórólapjaival. Használja ki a korlátozott idejű akciókat és vásároljon akciós áron!
	otherArticles: További cikkek
	author:
		title: Autor %author%
		description: Cikkek %author% szerzőtől
		articlesCount: %count% cikk|%count% cikkek|%count% cikk

showMore:
	offers: További termékek
	cities: További városok
	allCities: Minden város
	shops: További üzletek
	leaflets: További akciós újságok
	tags: Egyéb akciós áru

tabs:
	leaflets: Akciós újságok
	shop: Ügyfél előnyök
	product: Termékek
	contact: Kapcsolat

