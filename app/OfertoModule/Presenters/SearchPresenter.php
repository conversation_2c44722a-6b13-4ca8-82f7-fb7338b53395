<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON>\Debugger;

final class SearchPresenter extends BasePresenter
{
	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function actionSearch(?string $q = null): void
	{
		$this->template->query = $q;
		$this->template->shops = $this->shopFacade->findLeafletShopsByFulltext($this->localization, $q, true, 10, $this->website->getModule());
		$this->template->leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($q, $this->localization, $this->website->getModule());

        Debugger::log('Search query: ' . $q, 'search_oferto');
	}
}
