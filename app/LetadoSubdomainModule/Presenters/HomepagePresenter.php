<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Azure\AzureClient;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;

final class HomepagePresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function renderDefault(): void
	{
		$this->template->leaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 10, $this->website->getModule());
		$this->template->shops = $this->shopFacade->findTopLeafletShops($this->localization, true, 12, $this->website->getModule());
	}

	public function actionOther($other): void
	{
		$this->redirectPermanent('Homepage:default', ['region' => 'cz']);
	}
}
