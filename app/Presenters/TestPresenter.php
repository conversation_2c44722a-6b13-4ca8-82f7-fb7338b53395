<?php

namespace <PERSON><PERSON><PERSON>\Presenters;

use Nette;
use Nette\Caching\IStorage;
use Nette\Database\Context;

class TestPresenter extends Nette\Application\UI\Presenter
{
	/** @var IStorage @inject  */
	public $storage;

	/** @var Context @inject  */
	public $context;

	public function actionDefault()
	{
		$this->terminate();
	}

	public function actionCache()
	{
		$cache = new Nette\Caching\Cache($this->storage, self::class);

		for ($c = 1; $c <= 500; $c++) {
			$cache->load('key' . $c);
		}

		for ($c = 1; $c <= 500; $c++) {
			$cache->save('key' . $c, sha1($c), [
				Nette\Caching\Cache::EXPIRATION => '1 hour',
			]);
		}

		for ($c = 1; $c <= 500; $c++) {
			$cache->load('key' . $c);
		}


		for ($c = 1; $c <= 50; $c++) {
			$this->context->query('SELECT shop_id, count(id) FROM kaufino_leaflets_leaflet GROUP BY shop_id');
		}

		$this->sendJson(['status' => 'ok']);
	}
}
