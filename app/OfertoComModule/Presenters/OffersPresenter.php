<?php

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use Nette\Utils\Paginator;

class OffersPresenter extends BasePresenter
{
	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	public function renderOffers(int $page = 1)
	{
		$this->responseCacheTags[] = 'offers';

		$this->template->topShopsWithOffers = $this->shopFacade->findTopShopsWithOffers($this->localization);

		$paginator = new Paginator();
		$paginator->setPage($page);
		$paginator->setItemsPerPage(12);

		$this->template->offers = $this->offerFacade->findTopCouponOffers($this->localization, $paginator->getLength(), $paginator->getOffset());

		$this->template->paginator = $paginator;
	}
}
