<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;

final class ShopsPresenter extends BasePresenter
{
	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	public function actionShops(): void
	{
		$this->responseCacheTags[] = 'shops';

		$this->template->shops = $this->shopFacade->findTopCouponShops($this->localization, 100);
	}
}
