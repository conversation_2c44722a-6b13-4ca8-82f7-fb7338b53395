<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use DateTime;
use Ka<PERSON>ino\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\LeafletPage;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\ShopFacade;
use Nette\Database\Context;
use Tracy\Debugger;

class ProcessTopLeaflets extends Job
{
	/**
	 * @var LeafletFacade
	 */
	private $leafletFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var Context */
	private $context;

	public function __construct(
		LeafletFacade $leafletFacade,
		ShopFacade $shopFacade,
		LocalizationFacade $localizationFacade,
		EntityManager $em,
		Context $context
	) {
		parent::__construct();

		$this->leafletFacade = $leafletFacade;
		$this->shopFacade = $shopFacade;
		$this->localizationFacade = $localizationFacade;
		$this->em = $em;
		$this->context = $context;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-top-leaflets');
	}

	public function start(DateTime $fromDate = null, bool $current = false, int $offset = null): void
	{
		$log = $this->onStart();

		// $this->scheduleDetectObjects();

		$this->processTopLeaflets();

		$this->scheduleUseOcr();

		$this->onFinish($log);
	}

	private function processTopLeaflets(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets(null, false, null, null, true);

        $topLeafletIds = [];

        $leafletsByShop = [];
        foreach ($leaflets as $leaflet) {
            $leafletsByShop[$leaflet->getShop()->getId()][] = $leaflet;
        }

        /** @var Leaflet $leaflet */
        foreach ($leaflets as $leaflet) {
            $shopId = $leaflet->getShop()->getId();
            $now = new \DateTimeImmutable();

            if ($leaflet->hasOfferistaId()) {
                $topLeafletIds[$shopId] = $leaflet->getId();
                continue;
            }

            // Najdi nejbližší budoucí leták
            $closestFutureLeaflet = null;
            foreach ($leafletsByShop[$shopId] as $futureLeaflet) {
                if ($futureLeaflet->getValidSince() > $now) {
                    if ($closestFutureLeaflet === null || $futureLeaflet->getValidSince() < $closestFutureLeaflet->getValidSince()) {
                        $closestFutureLeaflet = $futureLeaflet;
                    }
                }
            }

            // Pokud existuje budoucí leták, který splňuje podmínky
            if ($closestFutureLeaflet !== null && $closestFutureLeaflet->getValidSince() <= $now->modify('+3 days')) {
                $topLeafletIds[$shopId] = $closestFutureLeaflet->getId();
            } else {
                // Jinak použij nejnovější aktuální leták
                $latestLeaflet = null;
                foreach ($leafletsByShop[$shopId] as $currentLeaflet) {
                    if ($currentLeaflet->getValidSince() <= $now) {
                        if ($latestLeaflet === null || $currentLeaflet->getValidSince() > $latestLeaflet->getValidSince()) {
                            $latestLeaflet = $currentLeaflet;
                        }
                    }
                }
                if ($latestLeaflet !== null) {
                    $topLeafletIds[$shopId] = $latestLeaflet->getId();
                }
            }
        }

        $topLeafletIds = array_values($topLeafletIds);

        $queries[] = 'UPDATE kaufino_leaflets_leaflet SET top = 0 WHERE top = 1 AND valid_till > NOW() AND id NOT IN (' . implode(',', $topLeafletIds) . ')';
        $queries[] = 'UPDATE kaufino_leaflets_leaflet SET top = 1 WHERE top = 0 AND valid_till > NOW() AND id IN (' . implode(',', $topLeafletIds) . ')';

        $connection = $this->em->getConnection();
        foreach ($queries as $query) {
            $statement = $connection->prepare($query);
            $statement->execute();
        }

		$this->leafletFacade->recomputeTop();
	}

	private function scheduleUseOcr(): void
	{
		$localizations = $this->localizationFacade->findLocalizationsByIds([1, 2, 3, 4, 5, 6, 8, 3, 17, 27, 9, 12, 14]);

		/** @var Localization $localization */
		foreach ($localizations as $localization) {
			$leaflets = $this->leafletFacade->findLeaflets($localization);

			/** @var Leaflet $leaflet */
			foreach ($leaflets as $leaflet) {
                $shop = $leaflet->getShop();

				if ($leaflet->isNewsletter()) {
					continue;
				}

                if ($shop->isUseOcr() === false) {
                    continue;
                }

                /** @var LeafletPage $page */
                foreach ($leaflet->getPages() as $page) {
					if ($page->isUseOcr()) {
						continue;
					}
					$page->setUseOcr(true);
					$this->leafletFacade->saveLeafletPage($page);
				}
			}
		}
	}

	private function scheduleDetectObjects(): void
	{
		Debugger::log("reset", 'process-top-leaflets');
		echo "start";

		$this->context->query('UPDATE kaufino_leaflets_leaflet_page lp SET lp.is_trending=0');

		/** @var Shop $shop */
		foreach ($this->shopFacade->getShops()->getQuery()->getResult() as $shop) {
			if (in_array($shop->getLocalization()->getId(), [1, 2, 4, 5, 6, 8, 3, 17, 27, 9, 14]) === false && in_array($shop->getId(), [3739, 421, 434, 506, 2272, 422, 488]) === false) {
				continue;
			}

			if ($shop->getCountOfPagesToUseDetectObjects() == 0 || $shop->getCountOfPagesToUseDetectObjects() == 0) {
				continue;
			}

			$leafletPages = $this->leafletFacade->findTopValidLeafletPagesByScore($shop, $shop->getCountOfPagesToUseDetectObjects());

			/** @var LeafletPage $leafletPage */
			foreach ($leafletPages as $leafletPage) {
				if ($leafletPage->isTrending() === true && $leafletPage->isUseDetectObjects() === true)  {
					continue;
				}

				$leafletPage->setIsTrending(true);
				$leafletPage->setUseDetectObjects(true);
				$this->leafletFacade->saveLeafletPage($leafletPage);
			}
		}
	}
}
