<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\SyncManager;

class ProcessCoupons extends Job
{
	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var SyncManager */
	private $syncManager;

	public function __construct(LocalizationFacade $localizationFacade, SyncManager $syncManager)
	{
		parent::__construct();

		$this->localizationFacade = $localizationFacade;
		$this->syncManager = $syncManager;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-coupons');
	}

	public function start($localizationId = null): void
	{
		$log = $this->onStart();

		$this->processCoupons(
            $localizationId !== null
                ? $this->localizationFacade->findLocalization($localizationId)
                : null
        );

		$this->onFinish($log);
	}

	private function processCoupons(?Localization $localization = null): void
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '900');

        if ($localization !== null) {
            $this->syncManager->syncCoupons($localization);
            return;
        }

		foreach ($this->localizationFacade->findByIds(1, 4, 5, 2, 6) as $localization) {
			$this->syncManager->syncCoupons($localization);
		}
	}
}
