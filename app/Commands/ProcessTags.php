<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON>\Debugger;

class ProcessTags extends Job
{
	/**
	 * @var TagFacade
	 */
	private $tagFacade;

	/**
	 * @var LeafletFacade
	 */
	private $leafletFacade;

	/**
	 * @var OfferFacade
	 */
	private $offerFacade;

	public function __construct(TagFacade $tagFacade, LeafletFacade $leafletFacade, OfferFacade $offerFacade)
	{
		parent::__construct();

		$this->tagFacade = $tagFacade;
		$this->leafletFacade = $leafletFacade;
		$this->offerFacade = $offerFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-tags');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processTagStats();

		$this->onFinish($log);
	}

	private function processTagStats(): void
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '300');

		$tags = $this->tagFacade->findTagsToUpdateStats(Tag::TYPE_OFFERS, 20);

		/** @var Tag $tag */
		foreach ($tags as $tag) {
			$leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($tag->getMatchRule(), $tag->getLocalization());
			$offers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), true);
			$expiredOffers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), false, 10);

			$tag->setCountOfLeaflets(count($leafletPages));
			$tag->setCountOfOffers(count($offers));
			$tag->setCountOfExpiredOffers(count($expiredOffers));
			$tag->updateStats();
			$this->tagFacade->saveTag($tag, false);

			/** @var Offer $offer */
			foreach ($expiredOffers as $offer) {
				//Debugger::dump("process tags");
				$offer->addTag($tag);
				$this->offerFacade->saveOffer($offer);
			}

			/** @var Offer $offer */
			foreach ($offers as $offer) {
				//Debugger::dump("process tags");
				$offer->addTag($tag);
				$this->offerFacade->saveOffer($offer);
			}
		}
	}
}
