<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Tags;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\ResponseCacheManager;
use <PERSON><PERSON>ino\Model\Tags\Entities\Tag;

class TagManager
{
	/** @var EntityManager */
	private $em;

	/** @var ResponseCacheManager */
	private $responseCacheManager;

	public function __construct(EntityManager $em, ResponseCacheManager $responseCacheManager)
	{
		$this->em = $em;
		$this->responseCacheManager = $responseCacheManager;
	}

	public function createTag(Localization $localization, string $name, string $slug): Tag
	{
		$tag = new Tag($localization, $name, $slug);

		return $this->saveTag($tag);
	}

	public function saveTag(Tag $tag, bool $resetCache = true): Tag
	{
		$this->em->persist($tag);
		$this->em->flush();

		if ($resetCache === true) {
			$this->responseCacheManager->clearCacheByTag('tag/' . $tag->getId());
		}

		return $tag;
	}
}
