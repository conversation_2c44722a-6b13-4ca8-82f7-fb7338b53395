<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Commands\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Commands\Repositories\LogRepository")
 * @ORM\Table(name="kaufino_commands_log")
 */
class Log
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\Column(type="string")
	 */
	private $command;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $fromConsole;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $note;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $processedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $finishedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct($command, $note = null)
	{
		$this->command = $command;
		$this->note = $note;

		$this->processedAt = new DateTime();
		$this->createdAt = new DateTime();

		$this->fromConsole = PHP_SAPI == 'cli' ? true : false;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getNote(): ?string
	{
		return $this->note;
	}

	public function setNote($note): void
	{
		$this->note = $note;
	}

	public function finish($note = null): void
	{
		$this->setNote($note);
		$this->finishedAt = new DateTime();
	}

	public function getCommand(): string
	{
		return $this->command;
	}

	public function getProcessedAt(): ?DateTime
	{
		return $this->processedAt;
	}

	public function getFinishedAt(): ?DateTime
	{
		return $this->finishedAt;
	}

	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}
}
