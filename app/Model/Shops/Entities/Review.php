<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops\Entities;

use <PERSON><PERSON>ino\Model\Users\Entities\User;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Shops\Repositories\ReviewRepository")
 * @ORM\Table(name="kaufino_shops_review")
 */
class Review
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="Shop", inversedBy="reviews")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	private $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $createdByUser;

	/**
	 * @ORM\Column(type="integer")
	 * @var int
	 */
	private $rate = 5;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string|null
	 */
	private $text;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	public function __construct(Shop $shop, int $rate, ?User $user = null, ?string $text = null)
	{
		$this->shop = $shop;
		$this->rate = $rate;
		$this->createdByUser = $user;
		$this->text = $text;

		$this->createdAt = new \DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getShop(): Shop
	{
		return $this->shop;
	}

	public function getCreatedByUser()
	{
		return $this->createdByUser;
	}

	public function getRate(): int
	{
		return $this->rate;
	}
}
