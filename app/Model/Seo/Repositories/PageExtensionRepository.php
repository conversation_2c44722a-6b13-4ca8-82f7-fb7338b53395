<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

class PageExtensionRepository extends EntityRepository
{
	public function getPageExtensions(Website $website = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('p');

		if ($website) {
			$qb->andWhere('p.website = :website')
				->setParameter('website', $website);
		}

		return $qb;
	}

	public function findPageExtensionBySlug(Website $website, string $slug)
	{
		$qb = $this->getPageExtensions($website)
			->andWhere('p.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}
}
