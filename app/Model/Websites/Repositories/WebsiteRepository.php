<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Websites\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class WebsiteRepository extends EntityRepository
{
	public function getWebsites()
	{
		return $this->createQueryBuilder('w')
			->addOrderBy('w.id');
	}

	public function findPairsList(): array
	{
		$list = [];

		foreach ($this->getWebsites()->getQuery()->getResult() as $website) {
			$list[$website->getId()] = $website->getName();
		}

		return $list;
	}
}
