<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Leaflets\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Kaufino\Model\Tags\Entities\Tag;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Leaflets\Repositories\LeafletPageRepository")
 * @ORM\Table(name="kaufino_leaflets_leaflet_page")
 */
class LeafletPage
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Leaflets\Entities\Leaflet", inversedBy="pages")
	 * @ORM\JoinColumn(name="leaflet_id", referencedColumnName="id")
	 */
	private $leaflet;

	/**
	 * @ORM\OneToMany(targetEntity="\Kaufino\Model\Offers\Entities\Offer", mappedBy="leafletPage")
	 */
	private $offers;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $imageUrl;

	/**
	 * @ORM\Column(type="integer", nullable=false)
	 */
	protected $pageNumber;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $ocrOutput;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $detectObjectsOutput;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    protected $clickOuts;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $score = null;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $isTrending = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $useOcr = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $useDetectObjects = false;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $annotationsOutput;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $timeToAnnotate;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $failedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $annotatedAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $annotatedImageRemovedAt;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	protected $annotator;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    protected $excludedFromTags = [];

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private ?string $description;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $processedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(Leaflet $leaflet, $imageUrl, $pageNumber)
	{
		$this->leaflet = $leaflet;
		$this->imageUrl = $imageUrl;
		$this->pageNumber = $pageNumber;
		$this->createdAt = new DateTime();
	}

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	public function getImageUrl(): ?string
	{
		return $this->imageUrl;
	}

    public function setPageNumber(int $pageNumber)
    {
        $this->pageNumber = $pageNumber;
    }

	public function getPageNumber(): int
	{
		return $this->pageNumber;
	}

	/**
	 * @return mixed
	 */
	public function getOcrOutput()
	{
		return $this->ocrOutput;
	}

	/**
	 * @param mixed $ocrOutput
	 */
	public function setOcrOutput($ocrOutput): void
	{
		$this->ocrOutput = $ocrOutput;
	}

	/**
	 * @return mixed
	 */
	public function getDetectObjectsOutput()
	{
		return $this->detectObjectsOutput;
	}

	/**
	 * @param mixed $detectObjectsOutput
	 */
	public function setDetectObjectsOutput($detectObjectsOutput): void
	{
		$this->detectObjectsOutput = $detectObjectsOutput;
	}

	/**
	 * @return string
	 */
	public function getAnnotationsOutput()
	{
		return $this->annotationsOutput;
	}

	/**
	 * @param string $annotationsOutput
	 */
	public function setAnnotationsOutput($annotationsOutput): void
	{
		$this->annotationsOutput = $annotationsOutput;
	}

	public function setAnnotated()
	{
		$this->annotatedAt = new DateTime();
	}

	public function setProcessed()
	{
		$this->processedAt = new DateTime();
	}

	/**
	 * @return Leaflet
	 */
	public function getLeaflet(): Leaflet
	{
		return $this->leaflet;
	}

	/**
	 * @return bool
	 */
	public function isUseOcr(): bool
	{
		return $this->useOcr;
	}

	/**
	 * @param bool $useOcr
	 */
	public function setUseOcr(bool $useOcr): void
	{
		$this->useOcr = $useOcr;
	}

	/**
	 * @return bool
	 */
	public function isUseDetectObjects(): bool
	{
		return $this->useDetectObjects;
	}

	/**
	 * @param bool $useDetectObjects
	 */
	public function setUseDetectObjects(bool $useDetectObjects): void
	{
		$this->useDetectObjects = $useDetectObjects;
	}

	/**
	 * @return mixed
	 */
	public function getFailedAt()
	{
		return $this->failedAt;
	}

	/**
	 * @param mixed $failedAt
	 */
	public function setFailedAt($failedAt): void
	{
		$this->failedAt = $failedAt;
	}

	public function fail(): void
	{
		$this->failedAt = new DateTime();
	}

	public function process(): void
	{
		$this->processedAt = new DateTime();
	}

    public function resetProcess()
    {
        $this->processedAt = null;
    }

	/**
	 * @return mixed
	 */
	public function getTimeToAnnotate()
	{
		return $this->timeToAnnotate;
	}

	/**
	 * @param mixed $timeToAnnotate
	 */
	public function setTimeToAnnotate($timeToAnnotate): void
	{
		$this->timeToAnnotate = $timeToAnnotate;
	}

	/**
	 * @return mixed
	 */
	public function getAnnotator()
	{
		return $this->annotator;
	}

	/**
	 * @param mixed $annotator
	 */
	public function setAnnotator($annotator): void
	{
		$this->annotator = $annotator;
	}

	/**
	 * @return bool
	 */
	public function isAnnotated(): bool
	{
		return $this->annotatedAt != null;
	}

	public function setImageUrl(?string $imageUrl): void
	{
		$this->imageUrl = $imageUrl;
	}

	public function getScore(): ?int
	{
		return $this->score;
	}

	public function setScore(int $score): void
	{
		$this->score = $score;
	}

	public function setIsTrending(bool $isTrending): void
	{
		$this->isTrending = $isTrending;
	}

	public function isTrending(): bool
	{
		return $this->isTrending;
	}

    public function getExcludedFromTags(): array
    {
        return $this->excludedFromTags;
    }

    public function hasExcludedTag(Tag $tag): bool
    {
        if ($this->excludedFromTags === null) {
            return false;
        }

        return in_array($tag->getId(), $this->excludedFromTags);
    }

    public function addExcludedTag(int $tagId): void
    {
        if ($this->excludedFromTags !== null && in_array($tagId, $this->excludedFromTags)) {
            return;
        }

        $this->excludedFromTags[] = $tagId;
    }

    public function setClickOuts(?string $clickOuts): void
    {
        $this->clickOuts = $clickOuts;
    }

    public function getClickOuts(): ?string
    {
        return $this->clickOuts;
    }

    public function getClickoutsArray()
    {
        return $this->clickOuts ? json_decode($this->clickOuts, true) : [];
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getOffers()
    {
        return $this->offers;
    }

    public function removeAnnotatedImage(): void
    {
        $this->annotatedImageRemovedAt = new DateTime();
    }
}
