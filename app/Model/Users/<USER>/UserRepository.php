<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Users\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Users\Entities\User;

class UserRepository extends EntityRepository
{
	public function getUsers(?Localization $localization = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('u');

		if ($localization) {
			$qb->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb;
	}

	public function findUserByEmail(string $email): ?User
	{
		$qb = $this->getUsers()
			->andWhere('u.email = :email')
			->setParameter('email', $email);

		return $qb->getQuery()->getOneOrNullResult();
	}

    public function findAuthors()
    {
        $qb = $this->getUsers()
            ->andWhere('u.role = :role')
            ->setParameter('role', User::ROLE_AUTHOR);

        return $qb->getQuery()->getResult();
    }
}
