<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Users;

use Ka<PERSON>ino\Model\Users\Entities\User;
use Ka<PERSON>ino\Model\Users\Repositories\UserRepository;
use Nette;

class UserIdentity
{
	/**
	 * @var UserRepository
	 */
	private $userRepository;

	/**
	 * @var User
	 */
	private $tempUser;
	/**
	 * @var Nette\Security\User
	 */
	private $user;

	public function __construct(UserRepository $userRepository, Nette\Security\User $user)
	{
		$this->userRepository = $userRepository;
		$this->user = $user;
	}

	public function getIdentity(): ?User
	{
		if (!$this->tempUser) {
			$this->tempUser = $this->userRepository->find($this->user->getId());
		}

		return $this->tempUser;
	}
}
