<?php

namespace <PERSON><PERSON><PERSON>\Model\Marketing;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Marketing\Entities\AdUnit;
use <PERSON><PERSON><PERSON>\Model\Marketing\Entities\Email;
use <PERSON><PERSON><PERSON>\Model\Marketing\Repositories\AdUnitRepository;
use <PERSON><PERSON><PERSON>\Model\Marketing\Repositories\EmailRepository;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;

class MarketingFacade
{
	/** @var EmailManager */
	private $emailManager;

	/** @var EmailRepository */
	private $emailRepository;
    private AdUnitRepository $adUnitRepository;

    public function __construct(EmailManager $emailManager, EmailRepository $emailRepository, AdUnitRepository $adUnitRepository)
	{
		$this->emailManager = $emailManager;
		$this->emailRepository = $emailRepository;
        $this->adUnitRepository = $adUnitRepository;
    }

	public function findEmail(string $email): ?Email
	{
		return $this->emailRepository->findEmail($email);
	}

	public function createEmail(string $email, ?Shop $shop): ?Email
	{
		return $this->emailManager->createEmail($email, $shop);
	}

    public function findAdUnitsByChannel(string $channel)
    {
        return $this->adUnitRepository->findAdUnitsByChannel($channel);
    }

    public function findAdUnitsByChannelByRoute(Localization $localization, string $channel, string $route): array
    {
        $adUnits = $this->adUnitRepository->findAdUnitsByChannel($localization, $channel);

        $result = [];

        /** @var AdUnit $adUnit */
        foreach ($adUnits as $adUnit) {
            if ($adUnit->getRoute() === $route) {
                $result[$adUnit->getElementId()] = $adUnit;
            }
        }

        return $result;
    }
}
