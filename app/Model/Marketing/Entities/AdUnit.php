<?php

namespace <PERSON><PERSON><PERSON>\Model\Marketing\Entities;

use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;

use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Strings;


/**
 * @ORM\Entity(repositoryClass="Ka<PERSON>ino\Model\Marketing\Repositories\AdUnitRepository")
 * @ORM\Table(name="kaufino_marketing_ad_units")
 */
class AdUnit
{
    /**
     * @var int
     * @ORM\Column(type="integer", nullable=FALSE)
     * @ORM\Id
     * @ORM\GeneratedValue
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
     * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
     */
    protected Localization $localization;

    /**
     * @ORM\Column(type="string", nullable=false)
     */
    protected string $size;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected ?string $route = null;

    /**
     * @ORM\Column(type="string", nullable=false)
     */
    protected string $channel;

    /**
     * @ORM\Column(type="string", nullable=false)
     */
    protected string $code;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected ?string $elementId;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $active = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $mobile = false;

    public function getSize(): string
    {
        return $this->size;
    }

    public function setSize(string $size): void
    {
        $this->size = $size;
    }

    public function getRoute(): ?string
    {
        return $this->route;
    }

    public function getChannel(): string
    {
        return $this->channel;
    }

    public function setChannel(string $channel): void
    {
        $this->channel = $channel;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getElementId(): ?string
    {
        return $this->elementId;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function isMobile(): bool
    {
        return $this->mobile;
    }

    public function setMobile(bool $mobile): void
    {
        $this->mobile = $mobile;
    }

    public function getAdUnitSizesAsArray(): array
    {
        $sizes = explode(',', $this->size);
        $result = [];

        foreach ($sizes as $size) {
            if ($size === null || Strings::contains($size, 'x') === false) {
                continue;
            }

            $dimensions = explode('x', $size);
            $result[] = [(int) $dimensions[0], (int) $dimensions[1]];
        }

        return $result;
    }
}