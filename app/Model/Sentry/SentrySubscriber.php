<?php

namespace <PERSON><PERSON><PERSON>\Model\Sentry;

use Contributte\Events\Extra\Event\Application\ErrorEvent;
use <PERSON><PERSON>ino\Model\Configuration;
use Nette;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\SmartObject;
use Sentry\State\Scope;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Tracy\Debugger;

final class SentrySubscriber implements EventSubscriberInterface
{
	use SmartObject;

	/** @var Nette\Http\Request  */
	private $httpRequest;

	/** @var Cache */
	private $cache;

	/** @var Configuration */
	private $configuration;

	public function __construct(
		Nette\Http\Request $httpRequest,
		Storage $storage,
		Configuration $configuration
	) {
		$this->httpRequest = $httpRequest;
		$this->cache = new Nette\Caching\Cache($storage, self::class);
		$this->configuration = $configuration;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			ErrorEvent::class => 'onError',
		];
	}

	public function onError(ErrorEvent $errorEvent)
	{
		if ($this->configuration->isDevelopmentMode()) {
			return;
		}

		if ($errorEvent->getThrowable() instanceof Nette\Application\BadRequestException) {
			return;
		}

		if (!\function_exists('\Sentry\init')) {
			return;
		}

		$throwable = $errorEvent->getThrowable();

		if (!($throwable instanceof \Exception)) {
			return;
		}

		$cacheKey = 'sentry-error';
		$cachedErrorsCount = $this->cache->load($cacheKey);

		if ($cachedErrorsCount && $cachedErrorsCount >= 1000) {
			Debugger::log($throwable->getMessage(), 'sentry-error-skipped');

			return;
		}

		$this->cache->save($cacheKey, $cachedErrorsCount ? $cachedErrorsCount + 1 : 1, [Nette\Caching\Cache::EXPIRATION => '12 hours']);

		\Sentry\init([
			'dsn' => 'https://<EMAIL>/6038606',
			'environment' => 'production',
		]);

		/** @var Nette\Http\Request $httpRequest */
		$httpRequest = $this->httpRequest;

		\Sentry\withScope(static function (Scope $scope) use ($errorEvent, $httpRequest): void {
			if (PHP_SAPI !== 'cli') {
				$scope->setContext('request', [
					'url' => $httpRequest->getUrl()->getAbsoluteUrl(),
				]);
			}

			\Sentry\captureException($errorEvent->getThrowable());
		});
	}
}
