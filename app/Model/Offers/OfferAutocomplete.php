<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Offers;

use <PERSON><PERSON>ino\Model\Offers\Repositories\OfferRepository;
use <PERSON><PERSON>ino\Model\Offers\Entities\Offer;
use <PERSON>\Debugger;

class OfferAutocomplete
{
	/**
	 * @var OfferRepository
	 */
	private $offerRepository;

	public function __construct(OfferRepository $offerRepository)
	{
		$this->offerRepository = $offerRepository;
	}

	public function resolveNameFromOcr(Offer $offer)
	{
        $tokens = $this->tokenizeString($offer->getOcrOutput());

        $ngrams = $this->generateAllNgrams($tokens);

        $ngrams = array_filter($ngrams, static function ($item) {
            return ctype_upper($item[0]) && strlen($item) >= 4 && strlen($item) <= 10;
        });

        $candidateOffers = $this->offerRepository->findOffersByNames($ngrams, $offer->getLocalization())->getResult();

        $candidateNames = array_map(static function ($candidateOffer) {
            return $candidateOffer->getName();
        }, $candidateOffers);

        usort($candidateNames, static function ($a, $b) {
            return strlen($b) <=> strlen($a);
        });

        return $candidateNames;
	}

    private function tokenizeString(string $input)
    {
        $tokens = explode(' ', $input);
        return array_filter($tokens);
    }

    private function generateAllNgrams(array $tokens)
    {
        $ngrams = [];
        $countOfTokens = count($tokens);

        for ($n = 1; $n <= $countOfTokens; $n++) {
            for ($i = 0; $i <= $countOfTokens - $n; $i++) {
                $ngrams[] = implode(' ', array_slice($tokens, $i, $n));
            }
        }

        return $ngrams;
    }
}
