<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use Contributte;
use Contributte\Translation\LocalesResolvers\ResolverInterface;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Nette;

class TranslatorResolver implements ResolverInterface
{
    use Nette\SmartObject;

	/** @var string */
	public static $parameter = 'region';

	/** @var Nette\Http\IRequest */
	private $request;

	/** @var Nette\Routing\Router */
	private $router;

	/**
	 * @var WebsiteFacade
	 */
	private $websiteFacade;

	public function __construct(Nette\Http\IRequest $request, Nette\Routing\Router $router, WebsiteFacade $websiteFacade)
	{
		$this->request = $request;
		$this->router = $router;
		$this->websiteFacade = $websiteFacade;
	}

	public function resolve(Contributte\Translation\Translator $translator): ?string
	{
/*        $match = $this->router->match($this->request);

		if ($match !== null && array_key_exists(self::$parameter, $match)) {
			$region = $match[self::$parameter];

			if ($region == Localization::REGION_CZECHIA) {
				return Localization::LOCALE_CZECH;
			}

			return $region;
		}*/

		$website = $this->websiteFacade->resolveCurrentWebsite();
		if ($website) {
			return $website->getLocalization()->getFullLocale();
		}

		return null;
	}
}
