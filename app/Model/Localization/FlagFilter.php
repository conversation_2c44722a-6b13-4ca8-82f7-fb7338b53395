<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use Nette\Http\Request;
use Kaufino\Model\Localization\Entities\Localization;

class FlagFilter
{
	/** @var Request */
	private $httpRequest;

	public function __construct(Request $httpRequest)
	{
		$this->httpRequest = $httpRequest;
	}

	public function __invoke($localization)
	{
		$flags = [
			1 => 'cs',
			2 => 'sk',
			3 => 'en',
		];

		if ($localization instanceof Localization) {
			$locale = $localization->getLocale();
		} elseif (is_numeric($localization)) {
			$locale = isset($flags[$localization]) ? $flags[$localization] : null;
		} elseif (is_string($localization)) {
			$locale = $localization;
		} else {
			return null;
		}

		return '<img src="' . $this->getBasePath() . '/images/flags/' . $locale . '.png" width="18px" alt="' . $locale . '">';
	}

	private function getBasePath()
	{
		$baseUri = $this->httpRequest ? rtrim($this->httpRequest->getUrl()->getBaseUrl(), '/') : null;

		return preg_replace('#https?://[^/]+#A', '', $baseUri);
	}
}
