<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Localization\Repositories\LocalizationRepository;
use Nette\Localization\ITranslator;

class LocalizationFacade
{
	/** @var array */
	private $cachedLocalizationsByLocale = [];

	/** @var ?Localization */
	private $currentLocalization;

	/** @var LocalizationRepository */
	private $localizationRepository;

	/** @var array  */
	private $tempLocalizations = [];

	/**
	 * @var ITranslator
	 */
	private $translator;
    private LocalizationManager $localizationManager;

    public function __construct(LocalizationRepository $localizationRepository, ITranslator $translator, LocalizationManager $localizationManager)
	{
		$this->localizationRepository = $localizationRepository;
		$this->translator = $translator;
        $this->localizationManager = $localizationManager;
    }

	public function getCurrentLocalization()
	{
		if (!$this->currentLocalization) {
			$this->currentLocalization = $this->findLocalizationByFullLocale($this->translator->getLocale());
		}

		return $this->currentLocalization;
	}

	public function findLocalization($id): ?Localization
	{
		/** @var ?Localization $localization */
		$localization = $this->localizationRepository->find($id);

		return $localization;
	}

	public function findLocalizationsByIds(array $ids): ?array
	{
		$qb = $this->localizationRepository->getLocalizations()
			->andWhere('l.id IN (:ids)')
			->setParameter('ids', $ids)
		;

		return $qb->getQuery()->getResult();
	}

	public function findLocalizationByFullLocale(string $locale): ?Localization
	{
		if (isset($this->cachedLocalizationsByLocale[$locale])) {
			return $this->cachedLocalizationsByLocale[$locale];
		}

		$this->cachedLocalizationsByLocale[$locale] = $this->localizationRepository->findOneBy(['fullLocale' => $locale]);

		/** @var ?Localization $localization */
		$localization = $this->cachedLocalizationsByLocale[$locale];

		return $localization;
	}

	public function findLocalizationByRegion(string $region): ?Localization
	{
		if (isset($this->tempLocalizations[$region])) {
			return $this->tempLocalizations[$region];
		}

		/** @var ?Localization $localization */
		$localization = $this->localizationRepository->findOneBy(['region' => $region]);
		$this->tempLocalizations[$region] = $localization;

		return $localization;
	}

	public function findLocalizations(): array
	{
		return $this->localizationRepository->findAll();
	}

    public function findLocalizationsToProcessLeaflets()
    {
        return $this->localizationRepository->findLocalizationsToProcessLeaflets();
    }

    public function saveLocalization(Localization $localization): Localization
    {
        return $this->localizationManager->saveLocalization($localization);
    }

	public function findActiveLocalizations(): array
	{
		return $this->localizationRepository->findBy(['active' => true]);
	}

	public function findPairs(): array
	{
		return $this->localizationRepository->findPairsList();
	}

	public function findByIds(...$ids)
	{
		return $this->localizationRepository->findBy(['id' => $ids]);
	}

    public function findLocalizationFromSteveLocale(string $locale): ?Localization
    {
        $region = $locale;

        if ($region == 'cs') {
            $region = Localization::REGION_CZECHIA;
        }

        if ($region == 'en') {
            $region = Localization::REGION_CANADA;
        }

        return $this->findLocalizationByRegion($region);
    }
}
