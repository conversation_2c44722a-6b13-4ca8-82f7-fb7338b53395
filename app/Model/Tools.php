<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use InvalidArgumentException;
use Nette\Http\FileUpload;
use Nette\Utils\Image;

class Tools
{
	public static function getImageTypeFromFileUpload(FileUpload $fileUpload)
	{
		switch ($fileUpload->getContentType()) {
			case 'image/png':
				$type = Image::PNG;
				break;
			case 'image/jpeg':
			case 'image/jpg':
				$type = Image::JPEG;
				break;
			default:
				throw new InvalidArgumentException('Neznámý typ souboru.'); // @todo pouzit kaufino vyjimku
		}

		return $type;
	}

	public static function stringToFloat(string $value): float
	{
		$value = str_replace(',', '.', trim($value));

		return (float) $value;
	}
}
