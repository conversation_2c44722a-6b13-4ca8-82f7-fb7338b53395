<?php

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON>ino\Model\Shops\Entities\Store;
use <PERSON><PERSON>ino\Model\Websites\Entities\Website;
use Ublaboo\DataGrid\DataGrid;

class StoresPresenter extends BasePresenter
{
    /** @var GeoFacade @inject */
    public $geoFacade;

    public function createComponentStoresGrid($name): DataGrid
    {
        $stores = $this->geoFacade->getStores();

        $grid = $this->dataGridFactory->create()
            ->getGrid($this, $name, $stores);

        $grid->setTemplateFile(__DIR__ . '/templates/Stores/grid/default.latte');

        $grid->setRememberState();

        $grid->addColumnText('localization', 'Localization')
            ->setRenderer(static function (Store $store) {
                return $store->getLocalization()->getName();
            })->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

        $grid->addColumnText('shop', 'Shop', 'shop.name')
            ->setRenderer(static function (Store $store) {
                return $store->getShop()->getName();
            })
            ->setFilterText();

        $grid->addColumnText('city', 'City', 'city.name')
            ->setRenderer(static function (Store $store) {
                return $store->getCity()->getName();
            })
            ->setFilterText();

        $grid->addColumnText('address', 'Address', 'fullAddress')
            ->setFilterText();

        $grid->addColumnText('slug', 'Slug')
            ->setFilterText();

        $grid->addColumnText('active', 'Active Kaufino')
            ->setTemplateEscaping(false)
            ->setAlign('center')
            ->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No'])
        ;

        $grid->addColumnText('activeOferto', 'Active Oferto')
            ->setTemplateEscaping(false)
            ->setAlign('center')
            ->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No'])
        ;

        return $grid;
    }

    public function handleActivate(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $store = $this->geoFacade->findStore($id);

        if ($websiteType === Website::MODULE_KAUFINO) {
            $store->activate();
        } else {
            $store->activateOferto();
        }

        $this->geoFacade->saveStore($store);

        $this['storesGrid']->reload();
    }

    public function handleDeactivate(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $store = $this->geoFacade->findStore($id);

        if ($websiteType === Website::MODULE_KAUFINO) {
            $store->deactivate();
        } else {
            $store->deactivateOferto();
        }

        $this->geoFacade->saveStore($store);

        $this['storesGrid']->reload();
    }

    public function handleVisit(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $store = $this->geoFacade->findStore($id);
        $website = $this->websiteFacade->findActiveWebsiteByLocalization($store->getLocalization(), $websiteType);
        $city = $store->getCity();
        $shop = $store->getShop();

        $url = $website->getDomain() . ($websiteType === Website::MODULE_OFERTO ? '/' : '') . $city->getSlug() . '/' . $shop->getSlug() . '/' . $store->getSlug();

        $this->redirectUrl($url);
    }
}