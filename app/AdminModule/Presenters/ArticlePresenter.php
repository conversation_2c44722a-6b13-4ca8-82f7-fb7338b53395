<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ArticleControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\IArticleControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ShopControl;
use Kaufino\Model\Articles\ArticleFacade;
use Kaufino\Model\Articles\Entities\Article;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;

class ArticlePresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var ArticleFacade @inject */
	public $articleFacade;

	/** @var IArticleControlFactory @inject */
	public $articleControlFactory;

	public function actionArticle($id = null)
	{
		if ($id) {
			/** @var Shop $article */
			$article = $this->articleFacade->findArticle($id);

			if (empty($article)) {
				$this->error('Article not found.');
			}

			$this->template->article = $article;
		}
	}

	public function createComponentArticlesGrid($name)
	{
		$articles = $this->articleFacade->getArticles();

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $articles);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Article/grid/default.latte');

		$grid->addToolbarButton(':Admin:Article:article')
			->setTitle('Add a new record')
			->setIcon('plus')
			->setClass('btn btn-xs btn-primary');

		$grid->addColumnText('website', 'Localization')
			->setRenderer(static function (Article $article) {
				return $article->getWebsite()->getName();
			})->setFilterSelect(['' => 'All'] + $this->websiteFacade->findPairs());

		$grid->addColumnText('image', 'Image');

		$grid->addColumnText('name', 'Name');
		$grid->addColumnText('slug', 'Slug');

		$grid->addColumnStatus('active', 'Active')
			->setFilterText();

		$grid->addFilterText('name', 'Name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('a.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addAction('article', '', 'article')->setClass('btn btn-xs btn-primary')->setIcon('pencil');

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['id' => 'DESC']);
	}

	protected function createComponentArticleControl(): ArticleControl
	{
		$article = $this->getParameter('id') ? $this->articleFacade->findArticle($this->getParameter('id')) : null;
		$control = $this->articleControlFactory->create($article);

		$control->onSuccess[] = function ($entity, $continue) use ($article) {
			$this->flashMessage('Successfully saved.');
			if ($article && !$continue) {
				$this->redirect(':Admin:Article:default');
			} else {
				$this->redirect(':Admin:Article:article', $entity->getId());
			}
		};

		return $control;
	}
}
