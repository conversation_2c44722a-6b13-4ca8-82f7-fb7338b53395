<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;
use <PERSON><PERSON>ino\Model\Shops\ShopFacade;

class TrendingLeafletPagesPresenter extends BasePresenter
{
	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	public function renderDefault(?int $localizationId = null)
	{
		if ($localizationId === null) {
			$this->redirect('this', ['localizationId' => 1]);
		}

		$this->template->localizations = $this->localizationFacade->findLocalizationsByIds([1, 2, 3, 4, 5, 6, 8, 14]);

		/** @var Localization $localization */
		$localization = $this->localizationFacade->findLocalization($localizationId);
		$this->template->currentLocalization = $localization;

		$this->template->shops = [];

		/** @var Shop $shop */
		foreach ($this->shopFacade->getShops($localization)->getQuery()->getResult() as $shop) {
			if ($shop->getCountOfPagesToUseDetectObjects() === null || $shop->getCountOfPagesToUseDetectObjects() === 0) {
				continue;
			}

			$this->template->shops[] = $shop;
		}

		$this->template->getTrendingLeafletPages = (function(Shop $shop): ?array {
			return $this->leafletFacade->findTrendingLeafletPages($shop);
		});
	}

}