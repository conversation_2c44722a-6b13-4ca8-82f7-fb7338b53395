<?php

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;

class CitiesPresenter extends BasePresenter
{
    /** @var GeoFacade @inject */
    public $geoFacade;

    /** @var LocalizationFacade @inject */
    public $localizationFacade;

    /** @var WebsiteFacade @inject */
    public $websiteFacade;

    public function createComponentCitiesGrid($name)
    {
        $cities = $this->geoFacade->getCities()
            ->leftJoin('c.shops', 'shops')
        ;

        $grid = $this->dataGridFactory->create()
            ->getGrid($this, $name, $cities);

        $grid->setTemplateFile(__DIR__ . '/templates/Cities/grid/default.latte');

        $grid->setRememberState(true);

        $grid->addColumnText('localization', 'Localization')
            ->setRenderer(static function (City $city) {
                return $city->getLocalization()->getName();
            })->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

        $grid->addColumnText('id', 'Id')
            ->setFilterText();

        $grid->addColumnText('name', 'Name')
            ->setFilterText();

        $grid->addColumnText('slug', 'Slug')
            ->setFilterText();

        $grid->addColumnText('population', 'Population')
            ->setSortable();

        $grid->addColumnNumber('brands', 'Brands')
            ->setRenderer(static function (City $city) {
                return $city->getShops()->count();
            })
            ->setSortable('shops.id')
        ;

        $grid->addColumnText('active', 'Active Kaufino')
            ->setTemplateEscaping(false)
            ->setAlign('center')
            ->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No'])
        ;

        $grid->addColumnText('activeBrandsKaufino', 'Active Brands Kaufino')
            ->setTemplateEscaping(false)
            ->setAlign('center')
            ->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No'])
        ;

        $grid->addColumnText('activatedAt', 'Activated Kaufino')
            ->setRenderer(static function (City $city) {
                return $city->getActivatedAt() ? $city->getActivatedAt()->format('d.m.Y H:i') : '';
            })
            ->setAlign('center')
            ->setSortable()
        ;

        $grid->addColumnText('activeOferto', 'Active Oferto')
            ->setTemplateEscaping(false)
            ->setAlign('center')
            ->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No'])
        ;

        $grid->addColumnText('activeBrandsOferto', 'Active Brands Oferto')
            ->setTemplateEscaping(false)
            ->setAlign('center')
            ->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No'])
        ;

        $grid->addColumnText('activatedOfertoAt', 'Activated Oferto')
            ->setRenderer(static function (City $city) {
                return $city->getActivatedOfertoAt() ? $city->getActivatedOfertoAt()->format('d.m.Y H:i') : '';
            })
            ->setAlign('center')
            ->setSortable()
        ;

        return $grid;
    }

    public function handleActivate(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $city = $this->geoFacade->findCity($id);

        if ($websiteType === Website::MODULE_KAUFINO) {
            $city->activate();
        } else {
            $city->activateOferto();
        }

        $this->geoFacade->saveCity($city);

        $this['citiesGrid']->reload();
    }

    public function handleActivateBrands(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $city = $this->geoFacade->findCity($id);

        if ($websiteType === Website::MODULE_KAUFINO) {
            $city->activateBrandsKaufino();
        } else {
            $city->activateBrandsOferto();
        }

        $this->geoFacade->saveCity($city);

        $this['citiesGrid']->reload();
    }

    public function handleDeactivate(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $city = $this->geoFacade->findCity($id);

        if ($websiteType === Website::MODULE_KAUFINO) {
            $city->deactivate();
        } else {
            $city->deactivateOferto();
        }

        $this->geoFacade->saveCity($city);

        $this['citiesGrid']->reload();
    }

    public function handleDeactivateBrands(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $city = $this->geoFacade->findCity($id);

        if ($websiteType === Website::MODULE_KAUFINO) {
            $city->deactivateBrandsKaufino();
        } else {
            $city->deactivateBrandsOferto();
        }

        $this->geoFacade->saveCity($city);

        $this['citiesGrid']->reload();
    }

    public function handleVisit(int $id, string $websiteType = Website::MODULE_KAUFINO)
    {
        $city = $this->geoFacade->findCity($id);
        $website = $this->websiteFacade->findActiveWebsiteByLocalization($city->getLocalization(), $websiteType);

        $this->redirectUrl($website->getDomain() . ($websiteType === Website::MODULE_OFERTO ? '/' : '') . $city->getSlug());
    }
}